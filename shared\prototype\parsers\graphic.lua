local GraphicType = {
	Static = "static",
	Animated = "animated",
	NineSlice = "nineSlice",
}

return function(node)
	local data = {
		id = node._attr and node._attr.Id and node._attr.Id,
		type = "graphic",

		fileName = node.FileName or "resources/textures/core/error.png",
		graphicType = GraphicType[node._attr.Type] or GraphicType.Static,
		frameWidth = node.FrameWidth or 32,
		frameHeight = node.FrameHeight or 32,

		wrapMode = node.WrapMode or "clamp",
		filterMin = node.FilterMin or "nearest",
		filterMag = node.FilterMag or "nearest",

		frameCount = node.FrameCount or 1,
		loopDelay = node.LoopDelay or 0,
		fps = node.FPS or 10,
	}

	local img = LS13.AssetManager.Loader.Load(data.fileName)
	img:setWrap(data.wrapMode)
	img:setFilter(data.filterMin, data.filterMag)

	if data.graphicType == GraphicType.NineSlice then
		data.slice = {
			left = node.Left or 0,
			right = node.Right or 0,
			top = node.Top or 0,
			bottom = node.Bottom or 0,
		}
	end

	data.image = img
	LS13.AssetManager.Push(data, data.id)
end
